#!/bin/bash

# 启动Figma MCP服务器脚本
echo "正在启动Figma MCP服务器..."

# 检查是否设置了Figma访问令牌
if [ -z "$FIGMA_ACCESS_TOKEN" ]; then
    echo "错误: 请先设置FIGMA_ACCESS_TOKEN环境变量"
    echo "获取方法:"
    echo "1. 登录Figma"
    echo "2. 进入Settings > Account > Personal access tokens"
    echo "3. 生成新的访问令牌"
    echo "4. 运行: export FIGMA_ACCESS_TOKEN=your_token_here"
    exit 1
fi

# 安装MCP Figma服务器（如果尚未安装）
echo "检查MCP Figma服务器..."
if ! npm list -g @modelcontextprotocol/server-figma > /dev/null 2>&1; then
    echo "安装MCP Figma服务器..."
    npm install -g @modelcontextprotocol/server-figma
fi

# 启动服务器
echo "启动MCP服务器，端口: 3055, 频道: r3mu4bpw"
npx @modelcontextprotocol/server-figma --port 3055 --channel r3mu4bpw

echo "MCP服务器已启动，可以开始与Figma交互"
